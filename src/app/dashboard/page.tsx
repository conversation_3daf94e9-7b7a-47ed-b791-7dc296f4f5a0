'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/stores/useAuthStore'
import { motion } from 'framer-motion'
import RLSErrorAlert from '@/components/RLSErrorAlert'

export default function DashboardPage() {
  const { user, profile, loading, hasRLSError } = useAuthStore()
  const router = useRouter()

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/login')
    }

    // Rediriger vers l'onboarding si pas encore complété
    if (user && profile && !profile.onboardingCompleted) {
      console.log('❌ Onboarding non complété, redirection...', {
        user: !!user,
        profile: !!profile,
        onboardingCompleted: profile.onboardingCompleted
      })
      router.push('/onboarding')
    } else if (user && profile && profile.onboardingCompleted) {
      console.log('✅ Onboarding complété, affichage du dashboard', {
        user: !!user,
        profile: !!profile,
        onboardingCompleted: profile.onboardingCompleted
      })
    }
  }, [user, profile, loading, router])

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <motion.div
          className="flex flex-col items-center space-y-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-primary"></div>
          <p className="text-muted-foreground">Chargement de votre dashboard...</p>
        </motion.div>
      </div>
    )
  }

  if (!user) {
    return null // Redirection en cours
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Alerte RLS si erreur 406 */}
      {hasRLSError && (
        <div className="fixed top-0 left-0 right-0 z-50">
          <RLSErrorAlert />
        </div>
      )}

      {/* Temporary Simple Dashboard */}
      <div className="container mx-auto p-8">
        <h1 className="text-4xl font-bold mb-4">Dashboard Halal Invest</h1>
        <p className="text-lg mb-8">Bienvenue sur votre tableau de bord d'investissement halal!</p>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-2">Portefeuille</h2>
            <p className="text-3xl font-bold text-green-600">11,690€</p>
            <p className="text-sm text-gray-600">****% ce mois</p>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-2">Conformité Sharia</h2>
            <p className="text-3xl font-bold text-blue-600">98.2%</p>
            <p className="text-sm text-gray-600">Excellent niveau</p>
          </div>

          <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-2">Performance</h2>
            <p className="text-3xl font-bold text-purple-600">+14.5%</p>
            <p className="text-sm text-gray-600">Depuis le début</p>
          </div>
        </div>

        <div className="mt-8">
          <h2 className="text-2xl font-semibold mb-4">Actions Rapides</h2>
          <div className="space-x-4">
            <button className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
              Voir Analytics
            </button>
            <button className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600">
              Nouveau Portefeuille
            </button>
            <button className="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600">
              Screening Sharia
            </button>
          </div>
        </div>

        <div className="mt-8 p-4 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
          <p className="text-sm">
            <strong>Note:</strong> Ceci est une version simplifiée du dashboard.
            La version complète avec le nouveau design sera disponible une fois les problèmes de routing résolus.
          </p>
        </div>
      </div>
    </div>
  )
}
