import { cn } from "@/lib/utils"
import { ArrowUpRight, ArrowDownLeft, Shield, TrendingUp, PieChart, Plus, ArrowRight, Target } from "lucide-react"

interface PortfolioItem {
  id: string
  title: string
  description?: string
  balance: string
  type: "conservative" | "moderate" | "aggressive" | "sharia"
}

interface List01Props {
  totalBalance?: string
  portfolios?: PortfolioItem[]
  className?: string
}

const PORTFOLIOS: PortfolioItem[] = [
  {
    id: "1",
    title: "Portefeuille Conservateur",
    description: "Sukuk et obligations halal",
    balance: "8 459,45€",
    type: "conservative",
  },
  {
    id: "2",
    title: "Portefeuille Équilibré",
    description: "Actions et sukuk mixtes",
    balance: "15 230,80€",
    type: "moderate",
  },
  {
    id: "3",
    title: "Portefeuille Dynamique",
    description: "Actions halal croissance",
    balance: "12 850,00€",
    type: "aggressive",
  },
  {
    id: "4",
    title: "Fonds Sharia Gold",
    description: "Métaux précieux halal",
    balance: "5 200,00€",
    type: "sharia",
  },
]

export default function List01({ totalBalance = "41 740,25€", portfolios = PORTFOLIOS, className }: List01Props) {
  return (
    <div
      className={cn(
        "w-full max-w-xl mx-auto",
        "bg-white dark:bg-zinc-900/70",
        "border border-zinc-100 dark:border-zinc-800",
        "rounded-xl shadow-sm backdrop-blur-xl",
        className,
      )}
    >
      {/* Total Balance Section */}
      <div className="p-4 border-b border-zinc-100 dark:border-zinc-800">
        <p className="text-xs text-zinc-600 dark:text-zinc-400">Valeur Totale</p>
        <h1 className="text-2xl font-semibold text-zinc-900 dark:text-zinc-50">{totalBalance}</h1>
      </div>

      {/* Portfolios List */}
      <div className="p-3">
        <div className="flex items-center justify-between mb-2">
          <h2 className="text-xs font-medium text-zinc-900 dark:text-zinc-100">Vos Portefeuilles</h2>
        </div>

        <div className="space-y-1">
          {portfolios.map((portfolio) => (
            <div
              key={portfolio.id}
              className={cn(
                "group flex items-center justify-between",
                "p-2 rounded-lg",
                "hover:bg-zinc-100 dark:hover:bg-zinc-800/50",
                "transition-all duration-200",
              )}
            >
              <div className="flex items-center gap-2">
                <div
                  className={cn("p-1.5 rounded-lg", {
                    "bg-emerald-100 dark:bg-emerald-900/30": portfolio.type === "conservative",
                    "bg-blue-100 dark:bg-blue-900/30": portfolio.type === "moderate",
                    "bg-purple-100 dark:bg-purple-900/30": portfolio.type === "aggressive",
                    "bg-yellow-100 dark:bg-yellow-900/30": portfolio.type === "sharia",
                  })}
                >
                  {portfolio.type === "conservative" && (
                    <Shield className="w-3.5 h-3.5 text-emerald-600 dark:text-emerald-400" />
                  )}
                  {portfolio.type === "moderate" && <PieChart className="w-3.5 h-3.5 text-blue-600 dark:text-blue-400" />}
                  {portfolio.type === "aggressive" && (
                    <TrendingUp className="w-3.5 h-3.5 text-purple-600 dark:text-purple-400" />
                  )}
                  {portfolio.type === "sharia" && <Target className="w-3.5 h-3.5 text-yellow-600 dark:text-yellow-400" />}
                </div>
                <div>
                  <h3 className="text-xs font-medium text-zinc-900 dark:text-zinc-100">{portfolio.title}</h3>
                  {portfolio.description && (
                    <p className="text-[11px] text-zinc-600 dark:text-zinc-400">{portfolio.description}</p>
                  )}
                </div>
              </div>

              <div className="text-right">
                <span className="text-xs font-medium text-zinc-900 dark:text-zinc-100">{portfolio.balance}</span>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Updated footer with four buttons */}
      <div className="p-2 border-t border-zinc-100 dark:border-zinc-800">
        <div className="grid grid-cols-4 gap-2">
          <button
            type="button"
            className={cn(
              "flex items-center justify-center gap-2",
              "py-2 px-3 rounded-lg",
              "text-xs font-medium",
              "bg-zinc-900 dark:bg-zinc-50",
              "text-zinc-50 dark:text-zinc-900",
              "hover:bg-zinc-800 dark:hover:bg-zinc-200",
              "shadow-sm hover:shadow",
              "transition-all duration-200",
            )}
          >
            <Plus className="w-3.5 h-3.5" />
            <span>Créer</span>
          </button>
          <button
            type="button"
            className={cn(
              "flex items-center justify-center gap-2",
              "py-2 px-3 rounded-lg",
              "text-xs font-medium",
              "bg-zinc-900 dark:bg-zinc-50",
              "text-zinc-50 dark:text-zinc-900",
              "hover:bg-zinc-800 dark:hover:bg-zinc-200",
              "shadow-sm hover:shadow",
              "transition-all duration-200",
            )}
          >
            <TrendingUp className="w-3.5 h-3.5" />
            <span>Investir</span>
          </button>
          <button
            type="button"
            className={cn(
              "flex items-center justify-center gap-2",
              "py-2 px-3 rounded-lg",
              "text-xs font-medium",
              "bg-zinc-900 dark:bg-zinc-50",
              "text-zinc-50 dark:text-zinc-900",
              "hover:bg-zinc-800 dark:hover:bg-zinc-200",
              "shadow-sm hover:shadow",
              "transition-all duration-200",
            )}
          >
            <ArrowDownLeft className="w-3.5 h-3.5" />
            <span>Retirer</span>
          </button>
          <button
            type="button"
            className={cn(
              "flex items-center justify-center gap-2",
              "py-2 px-3 rounded-lg",
              "text-xs font-medium",
              "bg-zinc-900 dark:bg-zinc-50",
              "text-zinc-50 dark:text-zinc-900",
              "hover:bg-zinc-800 dark:hover:bg-zinc-200",
              "shadow-sm hover:shadow",
              "transition-all duration-200",
            )}
          >
            <ArrowRight className="w-3.5 h-3.5" />
            <span>Plus</span>
          </button>
        </div>
      </div>
    </div>
  )
}
