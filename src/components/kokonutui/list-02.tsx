import { cn } from "@/lib/utils"
import {
  ArrowUpRight,
  ArrowDownLeft,
  TrendingUp,
  Shield,
  <PERSON><PERSON>hart,
  type LucideIcon,
  ArrowRight,
} from "lucide-react"

interface InvestmentTransaction {
  id: string
  title: string
  amount: string
  type: "buy" | "sell" | "dividend"
  category: string
  icon: LucideIcon
  timestamp: string
  status: "completed" | "pending" | "failed"
  shariaCompliant: boolean
}

interface List02Props {
  transactions?: InvestmentTransaction[]
  className?: string
}

const categoryStyles = {
  stocks: "bg-blue-100 dark:bg-blue-800 text-blue-900 dark:text-blue-100",
  sukuk: "bg-green-100 dark:bg-green-800 text-green-900 dark:text-green-100",
  etf: "bg-purple-100 dark:bg-purple-800 text-purple-900 dark:text-purple-100",
  dividend: "bg-yellow-100 dark:bg-yellow-800 text-yellow-900 dark:text-yellow-100",
}

const TRANSACTIONS: InvestmentTransaction[] = [
  {
    id: "1",
    title: "Achat AAPL",
    amount: "1 250,00€",
    type: "buy",
    category: "stocks",
    icon: TrendingUp,
    timestamp: "Aujourd'hui, 14:45",
    status: "completed",
    shariaCompliant: true,
  },
  {
    id: "2",
    title: "Dividende MSFT",
    amount: "45,80€",
    type: "dividend",
    category: "dividend",
    icon: ArrowDownLeft,
    timestamp: "Aujourd'hui, 09:00",
    status: "completed",
    shariaCompliant: true,
  },
  {
    id: "3",
    title: "Achat Sukuk Malaisie",
    amount: "500,00€",
    type: "buy",
    category: "sukuk",
    icon: Shield,
    timestamp: "Hier",
    status: "pending",
    shariaCompliant: true,
  },
  {
    id: "4",
    title: "Vente TSLA",
    amount: "890,00€",
    type: "sell",
    category: "stocks",
    icon: ArrowUpRight,
    timestamp: "Hier, 16:30",
    status: "completed",
    shariaCompliant: true,
  },
  {
    id: "5",
    title: "Achat ETF Halal",
    amount: "300,00€",
    type: "buy",
    category: "etf",
    icon: PieChart,
    timestamp: "Il y a 2 jours",
    status: "completed",
    shariaCompliant: true,
  },
  {
    id: "6",
    title: "Dividende NVDA",
    amount: "28,50€",
    type: "dividend",
    category: "dividend",
    icon: ArrowDownLeft,
    timestamp: "Il y a 3 jours",
    status: "completed",
    shariaCompliant: true,
  },
]

export default function List02({ transactions = TRANSACTIONS, className }: List02Props) {
  return (
    <div
      className={cn(
        "w-full max-w-xl mx-auto",
        "bg-white dark:bg-zinc-900/70",
        "border border-zinc-100 dark:border-zinc-800",
        "rounded-xl shadow-sm backdrop-blur-xl",
        className,
      )}
    >
      <div className="p-4">
        <div className="flex items-center justify-between mb-3">
          <h2 className="text-sm font-semibold text-zinc-900 dark:text-zinc-100">
            Activité Récente
            <span className="text-xs font-normal text-zinc-600 dark:text-zinc-400 ml-1">(6 transactions)</span>
          </h2>
          <span className="text-xs text-zinc-600 dark:text-zinc-400">Ce Mois</span>
        </div>

        <div className="space-y-1">
          {transactions.map((transaction) => (
            <div
              key={transaction.id}
              className={cn(
                "group flex items-center gap-3",
                "p-2 rounded-lg",
                "hover:bg-zinc-100 dark:hover:bg-zinc-800/50",
                "transition-all duration-200",
              )}
            >
              <div
                className={cn(
                  "p-2 rounded-lg",
                  "bg-zinc-100 dark:bg-zinc-800",
                  "border border-zinc-200 dark:border-zinc-700",
                )}
              >
                <transaction.icon className="w-4 h-4 text-zinc-900 dark:text-zinc-100" />
              </div>

              <div className="flex-1 flex items-center justify-between min-w-0">
                <div className="space-y-0.5">
                  <h3 className="text-xs font-medium text-zinc-900 dark:text-zinc-100">{transaction.title}</h3>
                  <p className="text-[11px] text-zinc-600 dark:text-zinc-400">{transaction.timestamp}</p>
                </div>

                <div className="flex items-center gap-1.5 pl-3">
                  <span
                    className={cn(
                      "text-xs font-medium",
                      transaction.type === "incoming"
                        ? "text-emerald-600 dark:text-emerald-400"
                        : "text-red-600 dark:text-red-400",
                    )}
                  >
                    {transaction.type === "incoming" ? "+" : "-"}
                    {transaction.amount}
                  </span>
                  {transaction.type === "incoming" ? (
                    <ArrowDownLeft className="w-3.5 h-3.5 text-emerald-600 dark:text-emerald-400" />
                  ) : (
                    <ArrowUpRight className="w-3.5 h-3.5 text-red-600 dark:text-red-400" />
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="p-2 border-t border-zinc-100 dark:border-zinc-800">
        <button
          type="button"
          className={cn(
            "w-full flex items-center justify-center gap-2",
            "py-2 px-3 rounded-lg",
            "text-xs font-medium",
            "bg-gradient-to-r from-zinc-900 to-zinc-800",
            "dark:from-zinc-50 dark:to-zinc-200",
            "text-zinc-50 dark:text-zinc-900",
            "hover:from-zinc-800 hover:to-zinc-700",
            "dark:hover:from-zinc-200 dark:hover:to-zinc-300",
            "shadow-sm hover:shadow",
            "transform transition-all duration-200",
            "hover:-translate-y-0.5",
            "active:translate-y-0",
            "focus:outline-none focus:ring-2",
            "focus:ring-zinc-500 dark:focus:ring-zinc-400",
            "focus:ring-offset-2 dark:focus:ring-offset-zinc-900",
          )}
        >
          <span>View All Transactions</span>
          <ArrowRight className="w-3.5 h-3.5" />
        </button>
      </div>
    </div>
  )
}
