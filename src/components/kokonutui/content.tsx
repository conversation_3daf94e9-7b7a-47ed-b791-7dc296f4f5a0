"use client"

import { <PERSON><PERSON><PERSON>, TrendingUp, Shield } from "lucide-react"
import { useAuthStore } from "@/stores/useAuthStore"
import List01 from "./list-01"
import List02 from "./list-02"
import List03 from "./list-03"

export default function Content() {
  const { profile } = useAuthStore()

  // Calculate total portfolio value based on user's monthly budget and investment history
  const calculateTotalValue = () => {
    if (!profile) return "0€"

    const baseAmount = profile.monthlyBudget * 12 // Annual budget
    const currentInvestment = profile.currentInvestmentAmount || 0
    const totalValue = baseAmount + currentInvestment

    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(totalValue)
  }

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white dark:bg-[#0F0F12] rounded-xl p-6 flex flex-col border border-gray-200 dark:border-[#1F1F23]">
          <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-4 text-left flex items-center gap-2 ">
            <PieChart className="w-3.5 h-3.5 text-zinc-900 dark:text-zinc-50" />
            Portefeuilles Halal
          </h2>
          <div className="flex-1">
            <List01 className="h-full" totalBalance={calculateTotalValue()} />
          </div>
        </div>
        <div className="bg-white dark:bg-[#0F0F12] rounded-xl p-6 flex flex-col border border-gray-200 dark:border-[#1F1F23]">
          <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-4 text-left flex items-center gap-2">
            <TrendingUp className="w-3.5 h-3.5 text-zinc-900 dark:text-zinc-50" />
            Transactions Récentes
          </h2>
          <div className="flex-1">
            <List02 className="h-full" />
          </div>
        </div>
      </div>

      <div className="bg-white dark:bg-[#0F0F12] rounded-xl p-6 flex flex-col items-start justify-start border border-gray-200 dark:border-[#1F1F23]">
        <h2 className="text-lg font-bold text-gray-900 dark:text-white mb-4 text-left flex items-center gap-2">
          <Shield className="w-3.5 h-3.5 text-zinc-900 dark:text-zinc-50" />
          Conformité Sharia
        </h2>
        <List03 />
      </div>
    </div>
  )
}
